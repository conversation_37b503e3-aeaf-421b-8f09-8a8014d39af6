'use client'

import React, { useState, useEffect } from 'react';
import { fabric } from 'fabric';
import { DefaultModal } from '@/common/components/organisms';
import { Button } from '@/common/components/atoms';
import { Save, FolderOpen, Trash2, FileText } from 'lucide-react';
import toast from 'react-hot-toast';

interface SavedDesign {
  id: string;
  name: string;
  data: string;
  thumbnail: string;
  timestamp: number;
}

interface SaveLoadModalProps {
  isOpen: boolean;
  onClose: () => void;
  canvas: fabric.Canvas | null;
  mode: 'save' | 'load';
}

// Local storage functions
const getSavedDesigns = (): SavedDesign[] => {
  if (typeof window === 'undefined') return [];
  const stored = localStorage.getItem('canvas-saved-designs');
  return stored ? JSON.parse(stored) : [];
};

const saveDesign = (design: SavedDesign) => {
  if (typeof window === 'undefined') return;
  const designs = getSavedDesigns();
  const updated = [design, ...designs.filter(d => d.id !== design.id)].slice(0, 50); // Keep max 50 designs
  localStorage.setItem('canvas-saved-designs', JSON.stringify(updated));
};

const deleteDesign = (id: string) => {
  if (typeof window === 'undefined') return;
  const designs = getSavedDesigns();
  const updated = designs.filter(d => d.id !== id);
  localStorage.setItem('canvas-saved-designs', JSON.stringify(updated));
};

export const SaveLoadModal = ({ isOpen, onClose, canvas, mode }: SaveLoadModalProps) => {
  const [savedDesigns, setSavedDesigns] = useState<SavedDesign[]>([]);
  const [designName, setDesignName] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setSavedDesigns(getSavedDesigns());
      if (mode === 'save') {
        const now = new Date();
        setDesignName(`Design ${now.toLocaleDateString()} ${now.toLocaleTimeString()}`);
      }
    }
  }, [isOpen, mode]);

  const handleSave = async () => {
    if (!canvas || !designName.trim()) {
      toast.error('Please enter a design name');
      return;
    }

    setIsSaving(true);
    try {
      // Generate thumbnail
      const thumbnail = canvas.toDataURL({
        format: 'png',
        quality: 0.3,
        multiplier: 0.2
      });

      // Save canvas data
      const canvasData = JSON.stringify(canvas.toJSON());

      const design: SavedDesign = {
        id: Date.now().toString(),
        name: designName.trim(),
        data: canvasData,
        thumbnail,
        timestamp: Date.now()
      };

      saveDesign(design);
      setSavedDesigns(getSavedDesigns());
      toast.success('Design saved successfully!');
      onClose();
    } catch (error) {
      console.error('Save error:', error);
      toast.error('Failed to save design');
    } finally {
      setIsSaving(false);
    }
  };

  const handleLoad = async (design: SavedDesign) => {
    if (!canvas) return;

    setIsLoading(true);
    try {
      canvas.loadFromJSON(design.data, () => {
        canvas.renderAll();
        toast.success(`Loaded "${design.name}"`);
        onClose();
        setIsLoading(false);
      });
    } catch (error) {
      console.error('Load error:', error);
      toast.error('Failed to load design');
      setIsLoading(false);
    }
  };

  const handleDelete = (id: string, name: string) => {
    if (confirm(`Are you sure you want to delete "${name}"?`)) {
      deleteDesign(id);
      setSavedDesigns(getSavedDesigns());
      toast.success('Design deleted');
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  return (
    <DefaultModal
      isOpen={isOpen}
      onClose={onClose}
      maxWidth="max-w-2xl"
      maxHeight="max-h-[80vh]"
    >
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-violets-are-blue/20 rounded-lg">
            {mode === 'save' ? (
              <Save className="text-violets-are-blue" size={20} />
            ) : (
              <FolderOpen className="text-violets-are-blue" size={20} />
            )}
          </div>
          <div>
            <h3 className="text-white font-semibold text-lg">
              {mode === 'save' ? 'Save Design' : 'Load Design'}
            </h3>
            <p className="text-gray-400 text-sm">
              {mode === 'save' 
                ? 'Save your current canvas design' 
                : 'Choose a design to load'
              }
            </p>
          </div>
        </div>

        {mode === 'save' ? (
          <div className="space-y-4">
            <div>
              <label className="text-white text-sm font-medium mb-2 block">Design Name</label>
              <input
                type="text"
                value={designName}
                onChange={(e) => setDesignName(e.target.value)}
                placeholder="Enter design name..."
                className="w-full bg-neutral-800 text-white border border-neutral-600 rounded-lg p-3 focus:border-violets-are-blue focus:outline-none"
                disabled={isSaving}
              />
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                size="lg"
                onClick={onClose}
                className="flex-1"
                disabled={isSaving}
              >
                Cancel
              </Button>
              <Button
                variant="gradient"
                size="lg"
                onClick={handleSave}
                className="flex-1"
                disabled={isSaving || !designName.trim()}
              >
                <Save size={16} className="mr-2" />
                {isSaving ? 'Saving...' : 'Save Design'}
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {savedDesigns.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="mx-auto mb-3 text-gray-500" size={48} />
                <p className="text-gray-400">No saved designs</p>
                <p className="text-gray-500 text-sm mt-1">Save a design to see it here</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {savedDesigns.map((design) => (
                  <div
                    key={design.id}
                    className="bg-neutral-800 rounded-lg p-4 border border-neutral-700 hover:border-violets-are-blue transition-colors group"
                  >
                    <div className="aspect-video bg-neutral-700 rounded-lg mb-3 overflow-hidden">
                      <img
                        src={design.thumbnail}
                        alt={design.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="space-y-2">
                      <h4 className="text-white font-medium truncate">{design.name}</h4>
                      <p className="text-gray-400 text-xs">{formatDate(design.timestamp)}</p>
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleLoad(design)}
                          disabled={isLoading}
                          className="flex-1 bg-violets-are-blue hover:bg-violets-are-blue/80 text-white py-2 px-3 rounded-lg transition-colors text-sm font-medium disabled:opacity-50"
                        >
                          {isLoading ? 'Loading...' : 'Load'}
                        </button>
                        <button
                          onClick={() => handleDelete(design.id, design.name)}
                          className="p-2 text-gray-400 hover:text-red-400 transition-colors"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className="flex justify-end">
              <Button
                variant="outline"
                size="lg"
                onClick={onClose}
                disabled={isLoading}
              >
                Close
              </Button>
            </div>
          </div>
        )}
      </div>
    </DefaultModal>
  );
};
