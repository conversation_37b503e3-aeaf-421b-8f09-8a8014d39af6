'use client'

import React, { useState } from 'react';
import { fabric } from 'fabric';
import { DefaultModal } from '@/common/components/organisms';
import { Button } from '@/common/components/atoms';
import { Download, Image as ImageIcon, FileText } from 'lucide-react';
import toast from 'react-hot-toast';

interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  canvas: fabric.Canvas | null;
  onSave?: (imageUrl: string) => void;
}

export const ExportModal = ({ isOpen, onClose, canvas, onSave }: ExportModalProps) => {
  const [format, setFormat] = useState<'png' | 'jpg' | 'svg'>('png');
  const [quality, setQuality] = useState(1);
  const [scale, setScale] = useState(1);
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (!canvas) return;

    setIsExporting(true);
    try {
      let dataURL: string;
      
      if (format === 'svg') {
        // Export as SVG
        const svgData = canvas.toSVG();
        const blob = new Blob([svgData], { type: 'image/svg+xml' });
        dataURL = URL.createObjectURL(blob);
      } else {
        // Export as PNG or JPG
        dataURL = canvas.toDataURL({
          format: format,
          quality: quality,
          multiplier: scale
        });
      }

      // Create download link
      const link = document.createElement('a');
      link.download = `canvas-design.${format}`;
      link.href = dataURL;
      link.click();

      // Clean up object URL if it was created
      if (format === 'svg') {
        URL.revokeObjectURL(dataURL);
      }

      toast.success(`Canvas exported as ${format.toUpperCase()}!`);
      onClose();
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export canvas');
    } finally {
      setIsExporting(false);
    }
  };

  const handleSaveForPost = () => {
    if (!canvas || !onSave) return;

    setIsExporting(true);
    try {
      const dataURL = canvas.toDataURL({
        format: 'png',
        quality: 1,
        multiplier: 2
      });
      
      onSave(dataURL);
      toast.success('Canvas saved for post creation!');
      onClose();
    } catch (error) {
      console.error('Save error:', error);
      toast.error('Failed to save canvas');
    } finally {
      setIsExporting(false);
    }
  };

  const getFileSize = () => {
    if (!canvas) return 'Unknown';
    
    const dataURL = canvas.toDataURL({
      format: format,
      quality: quality,
      multiplier: scale
    });
    
    // Estimate file size (base64 is ~33% larger than binary)
    const sizeInBytes = (dataURL.length * 3) / 4;
    const sizeInKB = sizeInBytes / 1024;
    const sizeInMB = sizeInKB / 1024;
    
    if (sizeInMB > 1) {
      return `~${sizeInMB.toFixed(1)} MB`;
    } else {
      return `~${sizeInKB.toFixed(0)} KB`;
    }
  };

  return (
    <DefaultModal
      isOpen={isOpen}
      onClose={onClose}
      maxWidth="max-w-md"
    >
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-violets-are-blue/20 rounded-lg">
            <Download className="text-violets-are-blue" size={20} />
          </div>
          <div>
            <h3 className="text-white font-semibold text-lg">Export Canvas</h3>
            <p className="text-gray-400 text-sm">Choose your export settings</p>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <label className="text-white text-sm font-medium mb-3 block">Format</label>
            <div className="grid grid-cols-3 gap-2">
              {[
                { value: 'png', label: 'PNG', desc: 'Best quality' },
                { value: 'jpg', label: 'JPG', desc: 'Smaller size' },
                { value: 'svg', label: 'SVG', desc: 'Vector format' }
              ].map((option) => (
                <button
                  key={option.value}
                  onClick={() => setFormat(option.value as 'png' | 'jpg' | 'svg')}
                  className={`p-3 rounded-lg border transition-colors text-center ${
                    format === option.value
                      ? 'bg-violets-are-blue border-violets-are-blue text-white'
                      : 'border-neutral-600 text-gray-400 hover:border-violets-are-blue hover:text-white'
                  }`}
                >
                  <div className="font-medium text-sm">{option.label}</div>
                  <div className="text-xs opacity-75">{option.desc}</div>
                </button>
              ))}
            </div>
          </div>

          {format !== 'svg' && (
            <>
              <div>
                <label className="text-white text-sm font-medium mb-2 block">
                  Quality: {Math.round(quality * 100)}%
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={quality}
                  onChange={(e) => setQuality(Number(e.target.value))}
                  className="w-full accent-violets-are-blue"
                />
              </div>

              <div>
                <label className="text-white text-sm font-medium mb-2 block">
                  Scale: {scale}x
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="4"
                  step="0.5"
                  value={scale}
                  onChange={(e) => setScale(Number(e.target.value))}
                  className="w-full accent-violets-are-blue"
                />
              </div>
            </>
          )}

          <div className="bg-neutral-800 p-3 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-400">Estimated file size:</span>
              <span className="text-white font-medium">{getFileSize()}</span>
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              size="lg"
              onClick={onClose}
              className="flex-1"
              disabled={isExporting}
            >
              Cancel
            </Button>
            
            {onSave && (
              <Button
                variant="gradient"
                size="lg"
                onClick={handleSaveForPost}
                className="flex-1"
                disabled={isExporting}
              >
                <ImageIcon size={16} className="mr-2" />
                {isExporting ? 'Saving...' : 'Save for Post'}
              </Button>
            )}
            
            <Button
              variant="gradient"
              size="lg"
              onClick={handleExport}
              className="flex-1"
              disabled={isExporting}
            >
              <Download size={16} className="mr-2" />
              {isExporting ? 'Exporting...' : 'Download'}
            </Button>
          </div>
        </div>
      </div>
    </DefaultModal>
  );
};
