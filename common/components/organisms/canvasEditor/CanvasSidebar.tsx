'use client'

import React, { useState } from 'react';
import * as fabric from 'fabric';
import {
  Wand2,
  Upload,
  Type,
  Shapes,
  Clock,
  Image as ImageIcon,
  Square,
  Circle,
  Triangle,
  Minus,
  Star
} from 'lucide-react';
import { ImageStyles } from '@/common/constants';
import { getPath } from '@/common/utils/helpers';
import toast from 'react-hot-toast';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';

interface CanvasSidebarProps {
  canvas: fabric.Canvas | null;
  selectedTool: string;
  onToolChange: (tool: string) => void;
  agentId?: string;
  planId?: string;
  isMobile?: boolean;
}

interface SidebarTab {
  id: string;
  icon: React.ComponentType<{ size?: number }>;
  label: string;
  content: React.ComponentType<{
    canvas: fabric.Canvas | null;
    agentId?: string;
    planId?: string;
  }>;
}

// Placeholder components for each tab
const GenerateImagePanel = ({
  canvas,
  agentId,
  planId
}: {
  canvas: fabric.Canvas | null;
  agentId?: string;
  planId?: string;
}) => {
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState(ImageStyles[0]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');
  const { trackContentEvent } = useMixpanelEvent();

  const handleGenerate = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter an image description');
      return;
    }

    if (imagePrompt.length < 10) {
      setError('Description should be at least 10 characters');
      return;
    }

    if (imagePrompt.length > 400) {
      setError('Description should not exceed 400 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    setError('');
    setIsGenerating(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/post-image-gen`;

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify({
          description: imagePrompt,
          style: selectedStyle.option,
          planId: planId || 'canvas-editor',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to generate image: ${response.statusText}`);
      }

      const imageData = await response.json();

      if (imageData && imageData.filepath && canvas) {
        const imageUrl = getPath(imageData.filepath);

        // Add the generated image to the canvas
        fabric.FabricImage.fromURL(imageUrl).then((img: any) => {
          img.scaleToWidth(300);
          img.center();
          canvas.add(img);
          canvas.renderAll();
        });

        trackContentEvent('image', {
          prompt: imagePrompt,
          imageStyle: selectedStyle.option,
        });

        toast.success('Image generated and added to canvas!');
        setImagePrompt(''); // Clear the prompt after successful generation
      } else {
        throw new Error('Invalid response from image generation API');
      }
    } catch (error) {
      console.error('Error generating image:', error);
      toast.error('Failed to generate image');
      setError('Failed to generate image. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg mb-2">Generate Image</h3>
        <p className="text-gray-400 text-sm">Create images using AI</p>
      </div>
      <div className="space-y-4">
        <div>
          <label className="text-white text-sm font-medium mb-2 block">Description</label>
          <textarea
            value={imagePrompt}
            onChange={(e) => setImagePrompt(e.target.value)}
            placeholder="Describe the image you want to generate..."
            className="w-full h-24 p-3 bg-neutral-800 text-white rounded-lg border border-neutral-600 resize-none focus:border-violets-are-blue focus:outline-none transition-colors"
            disabled={isGenerating}
          />
          <div className="text-xs text-gray-400 mt-1">
            {imagePrompt.length}/400 characters
          </div>
        </div>

        <div>
          <label className="text-white text-sm font-medium mb-2 block">Style</label>
          <select
            value={selectedStyle.option}
            onChange={(e) => {
              const style = ImageStyles.find(s => s.option === e.target.value);
              if (style) setSelectedStyle(style);
            }}
            className="w-full bg-neutral-800 text-white border border-neutral-600 rounded-lg p-2 focus:border-violets-are-blue focus:outline-none"
            disabled={isGenerating}
          >
            {ImageStyles.map((style) => (
              <option key={style.option} value={style.option}>
                {style.label}
              </option>
            ))}
          </select>
        </div>

        {error && (
          <div className="text-red-400 text-sm bg-red-400/10 p-2 rounded-lg">
            {error}
          </div>
        )}

        <button
          onClick={handleGenerate}
          disabled={isGenerating || !imagePrompt.trim()}
          className="w-full bg-gradient-to-r from-violets-are-blue to-han-purple hover:from-violets-are-blue/90 hover:to-han-purple/90 text-white py-3 px-4 rounded-lg transition-all duration-200 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isGenerating ? 'Generating...' : 'Generate Image'}
        </button>
      </div>
    </div>
  );
};

const UploadImagePanel = ({ canvas }: { canvas: fabric.Canvas | null; agentId?: string; planId?: string; }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState('');
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return 'Please upload a valid image file (JPEG, PNG, GIF, or WebP)';
    }

    if (file.size > maxSize) {
      return 'File size must be less than 10MB';
    }

    return null;
  };

  const handleFileUpload = async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError('');
    setIsUploading(true);

    try {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imgUrl = event.target?.result as string;
        if (canvas) {
          fabric.FabricImage.fromURL(imgUrl).then((img: any) => {
            // Scale image to fit nicely on canvas
            const maxWidth = 400;
            const maxHeight = 400;

            if (img.width! > maxWidth || img.height! > maxHeight) {
              const scaleX = maxWidth / img.width!;
              const scaleY = maxHeight / img.height!;
              const scale = Math.min(scaleX, scaleY);
              img.scale(scale);
            }

            img.center();
            canvas.add(img);
            canvas.renderAll();

            // Add to recent uploads
            addToRecentUploads(imgUrl, file.name);

            toast.success('Image added to canvas!');
          });
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      setError('Failed to upload image. Please try again.');
      toast.error('Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg mb-2">Upload Image</h3>
        <p className="text-gray-400 text-sm">Add images from your device</p>
      </div>
      <div className="space-y-4">
        <div
          onClick={handleClick}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-all cursor-pointer ${
            isDragging
              ? 'border-violets-are-blue bg-violets-are-blue/10'
              : 'border-neutral-600 hover:border-violets-are-blue'
          } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <ImageIcon className="mx-auto mb-3 text-gray-400" size={40} />
          <p className="text-gray-400 text-sm mb-2">
            {isDragging ? 'Drop image here' : 'Drag & drop or click to upload'}
          </p>
          <p className="text-gray-500 text-xs">PNG, JPG, GIF, WebP up to 10MB</p>
          {isUploading && (
            <div className="mt-3">
              <div className="text-violets-are-blue text-sm">Uploading...</div>
            </div>
          )}
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleFileInputChange}
          disabled={isUploading}
        />

        {error && (
          <div className="text-red-400 text-sm bg-red-400/10 p-2 rounded-lg">
            {error}
          </div>
        )}

        <div className="text-xs text-gray-500">
          <p>• Supported formats: JPEG, PNG, GIF, WebP</p>
          <p>• Maximum file size: 10MB</p>
          <p>• Images will be automatically resized to fit the canvas</p>
        </div>
      </div>
    </div>
  );
};

const TextPanel = ({ canvas }: { canvas: fabric.Canvas | null; agentId?: string; planId?: string; }) => {
  const [fontFamily, setFontFamily] = useState('Arial');
  const [fontSize, setFontSize] = useState(24);
  const [textColor, setTextColor] = useState('#000000');
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [textAlign, setTextAlign] = useState('left');

  const fonts = [
    'Arial',
    'Helvetica',
    'Times New Roman',
    'Georgia',
    'Verdana',
    'Comic Sans MS',
    'Impact',
    'Trebuchet MS',
    'Courier New',
    'Palatino'
  ];

  const addText = () => {
    if (!canvas) return;

    const text = new fabric.Text('Click to edit', {
      left: 100,
      top: 100,
      fontFamily: fontFamily,
      fontSize: fontSize,
      fill: textColor,
      fontWeight: isBold ? 'bold' : 'normal',
      fontStyle: isItalic ? 'italic' : 'normal',
      underline: isUnderline,
      textAlign: textAlign as 'left' | 'center' | 'right',
    });

    canvas.add(text);
    canvas.setActiveObject(text);
    canvas.renderAll();
    toast.success('Text added to canvas!');
  };

  const updateSelectedText = () => {
    if (!canvas) return;

    const activeObject = canvas.getActiveObject();
    if (activeObject && activeObject.type === 'text') {
      const textObject = activeObject as fabric.Text;
      textObject.set({
        fontFamily: fontFamily,
        fontSize: fontSize,
        fill: textColor,
        fontWeight: isBold ? 'bold' : 'normal',
        fontStyle: isItalic ? 'italic' : 'normal',
        underline: isUnderline,
        textAlign: textAlign as 'left' | 'center' | 'right',
      });
      canvas.renderAll();
    }
  };

  // Update text when properties change
  React.useEffect(() => {
    updateSelectedText();
  }, [fontFamily, fontSize, textColor, isBold, isItalic, isUnderline, textAlign]);

  return (
    <div className="p-6">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg mb-2">Text Tools</h3>
        <p className="text-gray-400 text-sm">Add and customize text</p>
      </div>
      <div className="space-y-6">
        <button
          onClick={addText}
          className="w-full bg-gradient-to-r from-violets-are-blue to-han-purple hover:from-violets-are-blue/90 hover:to-han-purple/90 text-white py-3 px-4 rounded-lg transition-all duration-200 font-medium shadow-lg"
        >
          Add Text
        </button>

        <div className="space-y-4">
          <div>
            <label className="text-white text-sm font-medium mb-2 block">Font Family</label>
            <select
              value={fontFamily}
              onChange={(e) => setFontFamily(e.target.value)}
              className="w-full bg-neutral-800 text-white border border-neutral-600 rounded-lg p-2 focus:border-violets-are-blue focus:outline-none"
            >
              {fonts.map((font) => (
                <option key={font} value={font} style={{ fontFamily: font }}>
                  {font}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Font Size: {fontSize}px
            </label>
            <input
              type="range"
              min="12"
              max="120"
              value={fontSize}
              onChange={(e) => setFontSize(Number(e.target.value))}
              className="w-full accent-violets-are-blue"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Color</label>
            <input
              type="color"
              value={textColor}
              onChange={(e) => setTextColor(e.target.value)}
              className="w-full h-10 rounded-lg border border-neutral-600"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Text Style</label>
            <div className="flex gap-2">
              <button
                onClick={() => setIsBold(!isBold)}
                className={`px-3 py-2 rounded-lg border transition-colors ${
                  isBold
                    ? 'bg-violets-are-blue border-violets-are-blue text-white'
                    : 'border-neutral-600 text-gray-400 hover:border-violets-are-blue hover:text-white'
                }`}
              >
                <strong>B</strong>
              </button>
              <button
                onClick={() => setIsItalic(!isItalic)}
                className={`px-3 py-2 rounded-lg border transition-colors ${
                  isItalic
                    ? 'bg-violets-are-blue border-violets-are-blue text-white'
                    : 'border-neutral-600 text-gray-400 hover:border-violets-are-blue hover:text-white'
                }`}
              >
                <em>I</em>
              </button>
              <button
                onClick={() => setIsUnderline(!isUnderline)}
                className={`px-3 py-2 rounded-lg border transition-colors ${
                  isUnderline
                    ? 'bg-violets-are-blue border-violets-are-blue text-white'
                    : 'border-neutral-600 text-gray-400 hover:border-violets-are-blue hover:text-white'
                }`}
              >
                <u>U</u>
              </button>
            </div>
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Text Alignment</label>
            <div className="flex gap-2">
              {['left', 'center', 'right'].map((align) => (
                <button
                  key={align}
                  onClick={() => setTextAlign(align)}
                  className={`flex-1 px-3 py-2 rounded-lg border transition-colors capitalize ${
                    textAlign === align
                      ? 'bg-violets-are-blue border-violets-are-blue text-white'
                      : 'border-neutral-600 text-gray-400 hover:border-violets-are-blue hover:text-white'
                  }`}
                >
                  {align}
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="text-xs text-gray-500 bg-neutral-800 p-3 rounded-lg">
          <p className="mb-1">💡 <strong>Tips:</strong></p>
          <p>• Select text on canvas to modify its properties</p>
          <p>• Double-click text to edit content</p>
          <p>• Use Delete key to remove selected text</p>
        </div>
      </div>
    </div>
  );
};

const ElementsPanel = ({ canvas }: { canvas: fabric.Canvas | null; agentId?: string; planId?: string; }) => {
  const [fillColor, setFillColor] = useState('#7E5EF2');
  const [strokeColor, setStrokeColor] = useState('#000000');
  const [strokeWidth, setStrokeWidth] = useState(2);
  const [opacity, setOpacity] = useState(1);

  const addRectangle = () => {
    if (!canvas) return;
    const rect = new fabric.Rect({
      left: 100,
      top: 100,
      width: 100,
      height: 100,
      fill: fillColor,
      stroke: strokeColor,
      strokeWidth: strokeWidth,
      opacity: opacity
    });
    canvas.add(rect);
    canvas.setActiveObject(rect);
    canvas.renderAll();
    toast.success('Rectangle added!');
  };

  const addCircle = () => {
    if (!canvas) return;
    const circle = new fabric.Circle({
      left: 100,
      top: 100,
      radius: 50,
      fill: fillColor,
      stroke: strokeColor,
      strokeWidth: strokeWidth,
      opacity: opacity
    });
    canvas.add(circle);
    canvas.setActiveObject(circle);
    canvas.renderAll();
    toast.success('Circle added!');
  };

  const addTriangle = () => {
    if (!canvas) return;
    const triangle = new fabric.Triangle({
      left: 100,
      top: 100,
      width: 100,
      height: 100,
      fill: fillColor,
      stroke: strokeColor,
      strokeWidth: strokeWidth,
      opacity: opacity
    });
    canvas.add(triangle);
    canvas.setActiveObject(triangle);
    canvas.renderAll();
    toast.success('Triangle added!');
  };

  const addLine = () => {
    if (!canvas) return;
    const line = new fabric.Line([50, 100, 200, 100], {
      left: 100,
      top: 100,
      stroke: strokeColor,
      strokeWidth: strokeWidth,
      opacity: opacity
    });
    canvas.add(line);
    canvas.setActiveObject(line);
    canvas.renderAll();
    toast.success('Line added!');
  };

  const addStar = () => {
    if (!canvas) return;
    // Create a star using a polygon
    const starPoints = [];
    const outerRadius = 50;
    const innerRadius = 25;
    const numPoints = 5;

    for (let i = 0; i < numPoints * 2; i++) {
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const angle = (i * Math.PI) / numPoints;
      starPoints.push({
        x: Math.cos(angle) * radius,
        y: Math.sin(angle) * radius
      });
    }

    const star = new fabric.Polygon(starPoints, {
      left: 100,
      top: 100,
      fill: fillColor,
      stroke: strokeColor,
      strokeWidth: strokeWidth,
      opacity: opacity
    });
    canvas.add(star);
    canvas.setActiveObject(star);
    canvas.renderAll();
    toast.success('Star added!');
  };

  const updateSelectedElement = () => {
    if (!canvas) return;

    const activeObject = canvas.getActiveObject();
    if (activeObject && (activeObject.type === 'rect' || activeObject.type === 'circle' || activeObject.type === 'triangle' || activeObject.type === 'polygon')) {
      activeObject.set({
        fill: fillColor,
        stroke: strokeColor,
        strokeWidth: strokeWidth,
        opacity: opacity
      });
      canvas.renderAll();
    }
  };

  // Update selected element when properties change
  React.useEffect(() => {
    updateSelectedElement();
  }, [fillColor, strokeColor, strokeWidth, opacity]);

  return (
    <div className="p-6">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg mb-2">Elements</h3>
        <p className="text-gray-400 text-sm">Add shapes and elements</p>
      </div>
      <div className="space-y-6">
        <div>
          <label className="text-white text-sm font-medium mb-3 block">Basic Shapes</label>
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={addRectangle}
              className="bg-neutral-800 hover:bg-neutral-700 text-white py-3 px-3 rounded-lg transition-colors border border-neutral-600 hover:border-violets-are-blue flex flex-col items-center gap-2"
            >
              <Square size={20} />
              <span className="text-xs">Rectangle</span>
            </button>
            <button
              onClick={addCircle}
              className="bg-neutral-800 hover:bg-neutral-700 text-white py-3 px-3 rounded-lg transition-colors border border-neutral-600 hover:border-violets-are-blue flex flex-col items-center gap-2"
            >
              <Circle size={20} />
              <span className="text-xs">Circle</span>
            </button>
            <button
              onClick={addTriangle}
              className="bg-neutral-800 hover:bg-neutral-700 text-white py-3 px-3 rounded-lg transition-colors border border-neutral-600 hover:border-violets-are-blue flex flex-col items-center gap-2"
            >
              <Triangle size={20} />
              <span className="text-xs">Triangle</span>
            </button>
            <button
              onClick={addLine}
              className="bg-neutral-800 hover:bg-neutral-700 text-white py-3 px-3 rounded-lg transition-colors border border-neutral-600 hover:border-violets-are-blue flex flex-col items-center gap-2"
            >
              <Minus size={20} />
              <span className="text-xs">Line</span>
            </button>
          </div>
          <div className="mt-2">
            <button
              onClick={addStar}
              className="w-full bg-neutral-800 hover:bg-neutral-700 text-white py-3 px-3 rounded-lg transition-colors border border-neutral-600 hover:border-violets-are-blue flex items-center justify-center gap-2"
            >
              <Star size={20} />
              <span className="text-xs">Star</span>
            </button>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label className="text-white text-sm font-medium mb-2 block">Fill Color</label>
            <input
              type="color"
              value={fillColor}
              onChange={(e) => setFillColor(e.target.value)}
              className="w-full h-10 rounded-lg border border-neutral-600"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Stroke Color</label>
            <input
              type="color"
              value={strokeColor}
              onChange={(e) => setStrokeColor(e.target.value)}
              className="w-full h-10 rounded-lg border border-neutral-600"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Stroke Width: {strokeWidth}px
            </label>
            <input
              type="range"
              min="0"
              max="20"
              value={strokeWidth}
              onChange={(e) => setStrokeWidth(Number(e.target.value))}
              className="w-full accent-violets-are-blue"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Opacity: {Math.round(opacity * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={opacity}
              onChange={(e) => setOpacity(Number(e.target.value))}
              className="w-full accent-violets-are-blue"
            />
          </div>
        </div>

        <div className="text-xs text-gray-500 bg-neutral-800 p-3 rounded-lg">
          <p className="mb-1">💡 <strong>Tips:</strong></p>
          <p>• Select shapes on canvas to modify their properties</p>
          <p>• Use Delete key to remove selected shapes</p>
          <p>• Drag corners to resize shapes</p>
        </div>
      </div>
    </div>
  );
};

// Simple storage for recent uploads (in a real app, this would be in a global state or database)
const getRecentUploads = (): Array<{id: string, url: string, name: string, timestamp: number}> => {
  if (typeof window === 'undefined') return [];
  const stored = localStorage.getItem('canvas-recent-uploads');
  return stored ? JSON.parse(stored) : [];
};

const addToRecentUploads = (url: string, name: string) => {
  if (typeof window === 'undefined') return;
  const recent = getRecentUploads();
  const newUpload = {
    id: Date.now().toString(),
    url,
    name,
    timestamp: Date.now()
  };

  // Add to beginning and keep only last 20
  const updated = [newUpload, ...recent.filter(item => item.url !== url)].slice(0, 20);
  localStorage.setItem('canvas-recent-uploads', JSON.stringify(updated));
};

const RecentUploadsPanel = ({ canvas }: { canvas: fabric.Canvas | null; agentId?: string; planId?: string; }) => {
  const [recentUploads, setRecentUploads] = useState<Array<{id: string, url: string, name: string, timestamp: number}>>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load recent uploads on component mount
  React.useEffect(() => {
    setRecentUploads(getRecentUploads());
  }, []);

  const addImageToCanvas = (imageUrl: string, imageName: string) => {
    if (!canvas) return;

    setIsLoading(true);
    fabric.FabricImage.fromURL(imageUrl, { crossOrigin: 'anonymous' }).then((img: any) => {
      // Scale image to fit nicely on canvas
      const maxWidth = 400;
      const maxHeight = 400;

      if (img.width! > maxWidth || img.height! > maxHeight) {
        const scaleX = maxWidth / img.width!;
        const scaleY = maxHeight / img.height!;
        const scale = Math.min(scaleX, scaleY);
        img.scale(scale);
      }

      img.center();
      canvas.add(img);
      canvas.setActiveObject(img);
      canvas.renderAll();
      toast.success(`${imageName} added to canvas!`);
      setIsLoading(false);
    }).catch(() => {
      setIsLoading(false);
      toast.error('Failed to load image');
    });
  };

  const clearRecentUploads = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('canvas-recent-uploads');
      setRecentUploads([]);
      toast.success('Recent uploads cleared!');
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-white font-semibold text-lg">Recent Uploads</h3>
          {recentUploads.length > 0 && (
            <button
              onClick={clearRecentUploads}
              className="text-xs text-gray-400 hover:text-red-400 transition-colors"
            >
              Clear all
            </button>
          )}
        </div>
        <p className="text-gray-400 text-sm">Your recently uploaded images</p>
      </div>

      {recentUploads.length === 0 ? (
        <div className="text-gray-400 text-sm text-center py-12">
          <ImageIcon className="mx-auto mb-3 text-gray-500" size={32} />
          <p>No recent uploads</p>
          <p className="text-xs mt-1">Upload images to see them here</p>
        </div>
      ) : (
        <div className="space-y-3">
          {recentUploads.map((upload) => (
            <div
              key={upload.id}
              className="bg-neutral-800 rounded-lg p-3 border border-neutral-700 hover:border-violets-are-blue transition-colors cursor-pointer group"
              onClick={() => addImageToCanvas(upload.url, upload.name)}
            >
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-neutral-700 rounded-lg overflow-hidden flex-shrink-0">
                  <img
                    src={upload.url}
                    alt={upload.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-white text-sm font-medium truncate group-hover:text-violets-are-blue transition-colors">
                    {upload.name}
                  </p>
                  <p className="text-gray-400 text-xs">
                    {formatDate(upload.timestamp)}
                  </p>
                </div>
                <div className="text-gray-400 group-hover:text-violets-are-blue transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {isLoading && (
        <div className="text-center py-4">
          <div className="text-violets-are-blue text-sm">Adding image to canvas...</div>
        </div>
      )}

      <div className="text-xs text-gray-500 bg-neutral-800 p-3 rounded-lg mt-6">
        <p className="mb-1">💡 <strong>Tips:</strong></p>
        <p>• Click any image to add it to the canvas</p>
        <p>• Recent uploads are stored locally</p>
        <p>• Only the last 20 uploads are kept</p>
      </div>
    </div>
  );
};

export const CanvasSidebar = ({ canvas, selectedTool, onToolChange, agentId, planId, isMobile = false }: CanvasSidebarProps) => {
  const [activeTab, setActiveTab] = useState('generate');

  const tabs: SidebarTab[] = [
    { id: 'generate', icon: Wand2, label: 'Generate', content: GenerateImagePanel },
    { id: 'upload', icon: Upload, label: 'Upload', content: UploadImagePanel },
    { id: 'text', icon: Type, label: 'Text', content: TextPanel },
    { id: 'elements', icon: Shapes, label: 'Elements', content: ElementsPanel },
    { id: 'recent', icon: Clock, label: 'Recent', content: RecentUploadsPanel },
  ];

  const ActiveContent = tabs.find(tab => tab.id === activeTab)?.content || GenerateImagePanel;

  if (isMobile) {
    return (
      <div className="bg-neutral-900 border-t border-neutral-700 h-64">
        {/* Mobile Tab Navigation */}
        <div className="flex bg-neutral-800 border-b border-neutral-700">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 p-3 flex flex-col items-center gap-1 transition-all duration-200 border-b-2 ${
                  activeTab === tab.id
                    ? 'bg-violets-are-blue text-white border-violets-are-blue'
                    : 'text-gray-400 hover:text-white hover:bg-neutral-700 border-transparent'
                }`}
                title={tab.label}
              >
                <Icon size={16} />
                <span className="text-xs font-medium">{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Mobile Tab Content */}
        <div className="h-full overflow-y-auto bg-neutral-900">
          <div className="h-full">
            <ActiveContent canvas={canvas} agentId={agentId} planId={planId} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 bg-neutral-900 border-r border-neutral-700 flex h-full">
      {/* Tab Navigation */}
      <div className="w-16 bg-neutral-800 flex flex-col border-r border-neutral-700">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`p-3 flex flex-col items-center gap-1 transition-all duration-200 border-r-2 ${
                activeTab === tab.id
                  ? 'bg-violets-are-blue text-white border-violets-are-blue shadow-lg'
                  : 'text-gray-400 hover:text-white hover:bg-neutral-700 border-transparent'
              }`}
              title={tab.label}
            >
              <Icon size={18} />
              <span className="text-xs font-medium">{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto bg-neutral-900">
        <div className="h-full">
          <ActiveContent canvas={canvas} agentId={agentId} planId={planId} />
        </div>
      </div>
    </div>
  );
};
