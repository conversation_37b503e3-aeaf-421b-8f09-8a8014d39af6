'use client'

import React from 'react';
import { Button } from '@/common/components/atoms';
import {
  X,
  Save,
  Download,
  Undo,
  Redo,
  MousePointer,
  Type,
  Square,
  Circle,
  Image as ImageIcon,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  FolderOpen
} from 'lucide-react';

interface CanvasToolbarProps {
  onClose: () => void;
  onSave?: () => void;
  onExport: () => void;
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  selectedTool: string;
  onToolChange: (tool: string) => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onResetZoom?: () => void;
  onSaveDesign?: () => void;
  onLoadDesign?: () => void;
}

export const CanvasToolbar = ({
  onClose,
  onSave,
  onExport,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
  selectedTool,
  onToolChange,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onSaveDesign,
  onLoadDesign
}: CanvasToolbarProps) => {
  const tools = [
    { id: 'select', icon: MousePointer, label: 'Select' },
    { id: 'text', icon: Type, label: 'Text' },
    { id: 'rectangle', icon: Square, label: 'Rectangle' },
    { id: 'circle', icon: Circle, label: 'Circle' },
    { id: 'image', icon: ImageIcon, label: 'Image' },
  ];

  return (
    <div className="bg-neutral-800 border-b border-neutral-700 px-2 md:px-4 py-2 md:py-3 flex items-center justify-between">
      {/* Left side - Tools */}
      <div className="flex items-center gap-1 md:gap-2">
        <div className="hidden md:flex items-center gap-1 mr-4">
          {tools.map((tool) => {
            const Icon = tool.icon;
            return (
              <button
                key={tool.id}
                onClick={() => onToolChange(tool.id)}
                className={`p-2 rounded-lg transition-colors ${
                  selectedTool === tool.id
                    ? 'bg-violets-are-blue text-white'
                    : 'text-gray-400 hover:text-white hover:bg-neutral-700'
                }`}
                title={tool.label}
              >
                <Icon size={18} />
              </button>
            );
          })}
        </div>

        {/* History Controls */}
        <div className="flex items-center gap-1">
          <button
            onClick={onUndo}
            disabled={!canUndo}
            className="p-1.5 md:p-2 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Undo"
          >
            <Undo size={16} className="md:w-[18px] md:h-[18px]" />
          </button>
          <button
            onClick={onRedo}
            disabled={!canRedo}
            className="p-1.5 md:p-2 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Redo"
          >
            <Redo size={16} className="md:w-[18px] md:h-[18px]" />
          </button>
        </div>

        {/* Zoom Controls */}
        <div className="flex items-center gap-1 ml-2 md:ml-4">
          <button
            onClick={onZoomOut}
            className="p-1.5 md:p-2 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700"
            title="Zoom Out"
          >
            <ZoomOut size={16} className="md:w-[18px] md:h-[18px]" />
          </button>
          <button
            onClick={onResetZoom}
            className="p-1.5 md:p-2 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700"
            title="Reset Zoom"
          >
            <RotateCcw size={16} className="md:w-[18px] md:h-[18px]" />
          </button>
          <button
            onClick={onZoomIn}
            className="p-1.5 md:p-2 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700"
            title="Zoom In"
          >
            <ZoomIn size={16} className="md:w-[18px] md:h-[18px]" />
          </button>
        </div>
      </div>

      {/* Center - Title */}
      <div className="flex-1 text-center">
        <h1 className="text-white font-semibold text-sm md:text-lg">Canvas Editor</h1>
      </div>

      {/* Right side - Actions */}
      <div className="flex items-center gap-1 md:gap-2">
        {onLoadDesign && (
          <Button
            variant="outline"
            size="sm"
            onClick={onLoadDesign}
            className="text-white border-neutral-600 hover:bg-neutral-700 hidden md:flex"
          >
            <FolderOpen size={16} className="mr-2" />
            Load
          </Button>
        )}

        {onSaveDesign && (
          <Button
            variant="outline"
            size="sm"
            onClick={onSaveDesign}
            className="text-white border-neutral-600 hover:bg-neutral-700 hidden md:flex"
          >
            <Save size={16} className="mr-2" />
            Save
          </Button>
        )}

        {onSave && (
          <Button
            variant="outline"
            size="sm"
            onClick={onSave}
            className="text-white border-neutral-600 hover:bg-neutral-700 hidden md:flex"
          >
            <Save size={16} className="mr-2" />
            Save for Post
          </Button>
        )}

        <Button
          variant="outline"
          size="sm"
          onClick={onExport}
          className="text-white border-neutral-600 hover:bg-neutral-700 hidden md:flex"
        >
          <Download size={16} className="mr-2" />
          Export
        </Button>

        {/* Mobile action buttons */}
        <div className="flex md:hidden items-center gap-1">
          {onSaveDesign && (
            <button
              onClick={onSaveDesign}
              className="p-1.5 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700"
              title="Save"
            >
              <Save size={16} />
            </button>
          )}
          <button
            onClick={onExport}
            className="p-1.5 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700"
            title="Export"
          >
            <Download size={16} />
          </button>
        </div>

        <button
          onClick={onClose}
          className="p-1.5 md:p-2 rounded-lg text-gray-400 hover:text-white hover:bg-neutral-700"
          title="Close"
        >
          <X size={18} className="md:w-5 md:h-5" />
        </button>
      </div>
    </div>
  );
};
