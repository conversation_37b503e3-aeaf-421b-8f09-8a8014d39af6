'use client'

import React, { useState, useRef, useEffect } from 'react';
import * as fabric from 'fabric';
import { CanvasSidebar } from './CanvasSidebar';
import { CanvasToolbar } from './CanvasToolbar';
import { ExportModal } from './ExportModal';
import { SaveLoadModal } from './SaveLoadModal';
import { cn } from '@/common/utils/helpers';

interface CanvasEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave?: (imageUrl: string) => void;
  initialImage?: string;
  className?: string;
  agentId?: string;
  planId?: string;
}

export const CanvasEditor = ({
  isOpen,
  onClose,
  onSave,
  initialImage,
  className,
  agentId,
  planId
}: CanvasEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [fabricCanvas, setFabricCanvas] = useState<fabric.Canvas | null>(null);
  const [selectedTool, setSelectedTool] = useState<string>('select');
  const [canvasHistory, setCanvasHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false);
  const [isLoadModalOpen, setIsLoadModalOpen] = useState(false);

  // Initialize fabric canvas
  useEffect(() => {
    if (!canvasRef.current || !isOpen) return;

    // Determine canvas size based on screen size
    const isMobile = window.innerWidth < 768;
    const canvasWidth = isMobile ? Math.min(window.innerWidth - 32, 600) : 800;
    const canvasHeight = isMobile ? Math.min(window.innerHeight - 300, 400) : 600;

    const canvas = new fabric.Canvas(canvasRef.current, {
      width: canvasWidth,
      height: canvasHeight,
      backgroundColor: '#ffffff',
      selection: true,
      preserveObjectStacking: true,
    });

    // Enable zoom and pan functionality
    canvas.on('mouse:wheel', function(opt) {
      const delta = opt.e.deltaY;
      let zoom = canvas.getZoom();
      zoom *= 0.999 ** delta;
      if (zoom > 20) zoom = 20;
      if (zoom < 0.01) zoom = 0.01;
      canvas.zoomToPoint(new fabric.Point(opt.e.offsetX, opt.e.offsetY), zoom);
      opt.e.preventDefault();
      opt.e.stopPropagation();
    });

    // Pan functionality
    canvas.on('mouse:down', function(this: any, opt: any) {
      const evt = opt.e;
      if (evt.altKey === true) {
        this.isDragging = true;
        this.selection = false;
        this.lastPosX = evt.clientX;
        this.lastPosY = evt.clientY;
      }
    });

    canvas.on('mouse:move', function(this: any, opt: any) {
      if (this.isDragging) {
        const e = opt.e;
        const vpt = this.viewportTransform;
        vpt[4] += e.clientX - this.lastPosX;
        vpt[5] += e.clientY - this.lastPosY;
        this.requestRenderAll();
        this.lastPosX = e.clientX;
        this.lastPosY = e.clientY;
      }
    });

    canvas.on('mouse:up', function(this: any, _opt: any) {
      this.setViewportTransform(this.viewportTransform);
      this.isDragging = false;
      this.selection = true;
    });

    // Touch support for mobile devices
    let lastTouchDistance = 0;

    // @ts-ignore - Touch events not in types
    canvas.on('touch:gesture', function(e: any) {
      if (e.e.touches && e.e.touches.length === 2) {
        const touch1 = e.e.touches[0];
        const touch2 = e.e.touches[1];

        const distance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
        );

        const center = {
          x: (touch1.clientX + touch2.clientX) / 2,
          y: (touch1.clientY + touch2.clientY) / 2
        };

        if (lastTouchDistance > 0) {
          const scale = distance / lastTouchDistance;
          let zoom = canvas.getZoom() * scale;

          if (zoom > 20) zoom = 20;
          if (zoom < 0.01) zoom = 0.01;

          canvas.zoomToPoint(new fabric.Point(center.x, center.y), zoom);
        }

        lastTouchDistance = distance;
        e.e.preventDefault();
      }
    });

    // @ts-ignore - Touch events not in types
    canvas.on('touch:drag', function(this: any, e: any) {
      if (e.e.touches && e.e.touches.length === 1) {
        // Single finger drag for panning
        const touch = e.e.touches[0];
        if (this.lastTouchX && this.lastTouchY) {
          const vpt = this.viewportTransform;
          vpt[4] += touch.clientX - this.lastTouchX;
          vpt[5] += touch.clientY - this.lastTouchY;
          this.requestRenderAll();
        }
        this.lastTouchX = touch.clientX;
        this.lastTouchY = touch.clientY;
      }
    });

    // @ts-ignore - Touch events not in types
    canvas.on('touch:longpress', function(e: any) {
      // Long press to select objects on mobile
      const target = canvas.findTarget(e.e);
      if (target) {
        canvas.setActiveObject(target);
        canvas.renderAll();
      }
    });

    // Set up canvas event listeners for history
    canvas.on('object:modified', () => {
      saveCanvasState(canvas);
    });

    canvas.on('object:added', () => {
      saveCanvasState(canvas);
    });

    canvas.on('object:removed', () => {
      saveCanvasState(canvas);
    });

    // Delete key functionality
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete' || e.key === 'Backspace') {
        const activeObjects = canvas.getActiveObjects();
        if (activeObjects.length) {
          activeObjects.forEach(obj => canvas.remove(obj));
          canvas.discardActiveObject();
          canvas.renderAll();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    setFabricCanvas(canvas);

    // Load initial image if provided
    if (initialImage) {
      fabric.FabricImage.fromURL(initialImage).then((img: any) => {
        img.scaleToWidth(400);
        img.center();
        canvas.add(img);
        canvas.renderAll();
      });
    }

    // Save initial state
    saveCanvasState(canvas);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      canvas.dispose();
    };
  }, [isOpen, initialImage]);

  const saveCanvasState = (canvas: fabric.Canvas) => {
    const state = JSON.stringify(canvas.toJSON());
    setCanvasHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(state);
      return newHistory;
    });
    setHistoryIndex(prev => prev + 1);
  };

  const undo = () => {
    if (historyIndex > 0 && fabricCanvas) {
      const prevState = canvasHistory[historyIndex - 1];
      fabricCanvas.loadFromJSON(prevState, () => {
        fabricCanvas.renderAll();
        setHistoryIndex(prev => prev - 1);
      });
    }
  };

  const redo = () => {
    if (historyIndex < canvasHistory.length - 1 && fabricCanvas) {
      const nextState = canvasHistory[historyIndex + 1];
      fabricCanvas.loadFromJSON(nextState, () => {
        fabricCanvas.renderAll();
        setHistoryIndex(prev => prev + 1);
      });
    }
  };

  const handleSave = () => {
    if (fabricCanvas && onSave) {
      const dataURL = fabricCanvas.toDataURL({
        format: 'png',
        quality: 1,
        multiplier: 2
      });
      onSave(dataURL);
    }
  };

  const handleExport = () => {
    setIsExportModalOpen(true);
  };

  const handleZoomIn = () => {
    if (fabricCanvas) {
      const zoom = fabricCanvas.getZoom();
      fabricCanvas.setZoom(Math.min(zoom * 1.1, 20));
    }
  };

  const handleZoomOut = () => {
    if (fabricCanvas) {
      const zoom = fabricCanvas.getZoom();
      fabricCanvas.setZoom(Math.max(zoom * 0.9, 0.01));
    }
  };

  const handleResetZoom = () => {
    if (fabricCanvas) {
      fabricCanvas.setZoom(1);
      fabricCanvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
    }
  };

  const handleSaveDesign = () => {
    setIsSaveModalOpen(true);
  };

  const handleLoadDesign = () => {
    setIsLoadModalOpen(true);
  };

  if (!isOpen) return null;

  return (
    <div className={cn(
      "fixed inset-0 z-50 bg-neutral-900 flex flex-col",
      className
    )}>
      {/* Header/Toolbar */}
      <CanvasToolbar
        onClose={onClose}
        onSave={handleSave}
        onExport={handleExport}
        onUndo={undo}
        onRedo={redo}
        canUndo={historyIndex > 0}
        canRedo={historyIndex < canvasHistory.length - 1}
        selectedTool={selectedTool}
        onToolChange={setSelectedTool}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onResetZoom={handleResetZoom}
        onSaveDesign={handleSaveDesign}
        onLoadDesign={handleLoadDesign}
      />

      <div className="flex flex-1 min-h-0 flex-col md:flex-row">
        {/* Left Sidebar - Hidden on mobile, shown as bottom sheet */}
        <div className="hidden md:block">
          <CanvasSidebar
            canvas={fabricCanvas}
            selectedTool={selectedTool}
            onToolChange={setSelectedTool}
            agentId={agentId}
            planId={planId}
          />
        </div>

        {/* Main Canvas Area */}
        <div className="flex-1 flex flex-col items-center justify-center bg-neutral-800 p-2 md:p-4">
          <div className="bg-white rounded-lg shadow-2xl p-2 md:p-4 w-full max-w-4xl">
            <canvas
              ref={canvasRef}
              className="border border-gray-200 rounded w-full max-w-full"
              style={{ maxHeight: 'calc(100vh - 200px)' }}
            />
          </div>
          <div className="mt-2 md:mt-4 text-center text-gray-400 text-xs md:text-sm px-2">
            <p className="hidden md:block">• Mouse wheel to zoom • Alt + drag to pan • Delete key to remove selected objects</p>
            <p className="md:hidden">• Pinch to zoom • Drag to pan • Tap to select</p>
          </div>
        </div>

        {/* Mobile Sidebar - Bottom sheet on mobile */}
        <div className="md:hidden">
          <CanvasSidebar
            canvas={fabricCanvas}
            selectedTool={selectedTool}
            onToolChange={setSelectedTool}
            agentId={agentId}
            planId={planId}
            isMobile={true}
          />
        </div>
      </div>

      {/* Export Modal */}
      <ExportModal
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        canvas={fabricCanvas}
        onSave={onSave}
      />

      {/* Save Design Modal */}
      <SaveLoadModal
        isOpen={isSaveModalOpen}
        onClose={() => setIsSaveModalOpen(false)}
        canvas={fabricCanvas}
        mode="save"
      />

      {/* Load Design Modal */}
      <SaveLoadModal
        isOpen={isLoadModalOpen}
        onClose={() => setIsLoadModalOpen(false)}
        canvas={fabricCanvas}
        mode="load"
      />
    </div>
  );
};
